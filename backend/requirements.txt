# Core web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# WebSocket support
websockets==12.0

# Database
sqlite3  # Built into Python
sqlalchemy==2.0.23
alembic==1.12.1

# PM5 Communication
pyserial==3.5
pyusb==1.2.1
bleak==0.21.1  # Bluetooth Low Energy

# Data processing
numpy==1.24.3
pandas==2.0.3

# Async support
asyncio  # Built into Python
aiofiles==23.2.1

# Validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client for testing
httpx==0.25.2
requests==2.31.0

# Logging and monitoring
structlog==23.2.0
python-json-logger==2.0.7

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Configuration
python-dotenv==1.0.0

# CORS support
python-cors==1.7.0

# Date/time handling
python-dateutil==2.8.2

# JSON handling
orjson==3.9.10

# Process management
psutil==5.9.6
