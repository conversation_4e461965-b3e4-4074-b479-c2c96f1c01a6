# Backend Configuration
DEBUG=true
LOG_LEVEL=INFO

# Server Configuration
HOST=localhost
PORT=8080
RELOAD=true

# Database Configuration
DATABASE_URL=sqlite:///./competition_data.db

# PM5 Communication
PM5_POLL_INTERVAL=0.1  # 10Hz polling
PM5_CONNECTION_TIMEOUT=5.0
PM5_MAX_DEVICES=10

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=50

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE"]
ALLOWED_HEADERS=["*"]

# Unity Configuration
UNITY_API_KEY=unity-api-key-here
UNITY_WS_ENDPOINT=/ws/unity

# Admin Frontend Configuration
ADMIN_WS_ENDPOINT=/ws/admin

# Competition Settings
MAX_COMPETITION_DURATION=3600  # 1 hour in seconds
MAX_PARTICIPANTS_PER_COMPETITION=20
DEFAULT_COUNTDOWN_DURATION=10  # seconds

# Data Export
EXPORT_FORMAT=csv
EXPORT_MAX_RECORDS=10000

# Monitoring
ENABLE_METRICS=true
METRICS_ENDPOINT=/metrics
