"""
Device management API endpoints
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel

from ..pm5 import PM5Manager
from ..database import get_db_session
from ..models import Device as DeviceModel, User as UserModel


logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response models
class DeviceRegistrationRequest(BaseModel):
    user_id: str
    user_name: str


class DeviceResponse(BaseModel):
    device_id: str
    status: str
    last_seen: datetime
    device_info: Dict[str, Any]
    current_user_id: Optional[str] = None
    connection_type: str


class DeviceListResponse(BaseModel):
    devices: List[DeviceResponse]
    total: int


def get_pm5_manager(request: Request) -> PM5Manager:
    """Dependency to get PM5 manager"""
    return request.app.state.pm5_manager


@router.get("/", response_model=DeviceListResponse)
async def get_devices(
    pm5_manager: PM5Manager = Depends(get_pm5_manager),
    db_session = Depends(get_db_session)
) -> DeviceListResponse:
    """Get all connected devices"""
    try:
        # Get devices from PM5 manager
        devices = []
        for device_id, device in pm5_manager.devices.items():
            device_response = DeviceResponse(
                device_id=device.device_id,
                status=device.status.value,
                last_seen=device.last_seen,
                device_info={
                    "model": device.device_info.model,
                    "serial_number": device.device_info.serial_number,
                    "firmware_version": device.device_info.firmware_version,
                    "hardware_version": device.device_info.hardware_version
                },
                current_user_id=device.current_user_id,
                connection_type=device.connection_type
            )
            devices.append(device_response)
        
        return DeviceListResponse(
            devices=devices,
            total=len(devices)
        )
    
    except Exception as e:
        logger.error(f"Error getting devices: {e}")
        raise HTTPException(status_code=500, detail="Failed to get devices")


@router.get("/{device_id}", response_model=DeviceResponse)
async def get_device(
    device_id: str,
    pm5_manager: PM5Manager = Depends(get_pm5_manager)
) -> DeviceResponse:
    """Get specific device by ID"""
    try:
        device = pm5_manager.get_device(device_id)
        if not device:
            raise HTTPException(status_code=404, detail="Device not found")
        
        return DeviceResponse(
            device_id=device.device_id,
            status=device.status.value,
            last_seen=device.last_seen,
            device_info={
                "model": device.device_info.model,
                "serial_number": device.device_info.serial_number,
                "firmware_version": device.device_info.firmware_version,
                "hardware_version": device.device_info.hardware_version
            },
            current_user_id=device.current_user_id,
            connection_type=device.connection_type
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting device {device_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get device")


@router.post("/{device_id}/register")
async def register_device(
    device_id: str,
    registration: DeviceRegistrationRequest,
    pm5_manager: PM5Manager = Depends(get_pm5_manager),
    db_session = Depends(get_db_session)
) -> Dict[str, str]:
    """Register a device with a user"""
    try:
        # Check if device exists
        device = pm5_manager.get_device(device_id)
        if not device:
            raise HTTPException(status_code=404, detail="Device not found")
        
        # Check if user exists, create if not
        user = db_session.query(UserModel).filter(UserModel.user_id == registration.user_id).first()
        if not user:
            user = UserModel(
                user_id=registration.user_id,
                name=registration.user_name
            )
            db_session.add(user)
            db_session.commit()
        
        # Register device with user
        success = await pm5_manager.register_device_user(device_id, registration.user_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to register device")
        
        return {"message": f"Device {device_id} registered to user {registration.user_name}"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error registering device {device_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to register device")


@router.post("/{device_id}/unregister")
async def unregister_device(
    device_id: str,
    pm5_manager: PM5Manager = Depends(get_pm5_manager)
) -> Dict[str, str]:
    """Unregister a device from its current user"""
    try:
        # Check if device exists
        device = pm5_manager.get_device(device_id)
        if not device:
            raise HTTPException(status_code=404, detail="Device not found")
        
        # Unregister device
        success = await pm5_manager.unregister_device(device_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to unregister device")
        
        return {"message": f"Device {device_id} unregistered"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error unregistering device {device_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to unregister device")


@router.post("/{device_id}/reconnect")
async def reconnect_device(
    device_id: str,
    pm5_manager: PM5Manager = Depends(get_pm5_manager)
) -> Dict[str, str]:
    """Attempt to reconnect a disconnected device"""
    try:
        success = await pm5_manager.reconnect_device(device_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to reconnect device")
        
        return {"message": f"Device {device_id} reconnection initiated"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reconnecting device {device_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to reconnect device")


@router.get("/{device_id}/status")
async def get_device_status(
    device_id: str,
    pm5_manager: PM5Manager = Depends(get_pm5_manager)
) -> Dict[str, Any]:
    """Get detailed device status and current metrics"""
    try:
        device = pm5_manager.get_device(device_id)
        if not device:
            raise HTTPException(status_code=404, detail="Device not found")
        
        # Get latest metrics
        latest_metrics = await pm5_manager.get_latest_metrics(device_id)
        
        return {
            "device_id": device.device_id,
            "status": device.status.value,
            "last_seen": device.last_seen,
            "current_user_id": device.current_user_id,
            "connection_type": device.connection_type,
            "latest_metrics": latest_metrics.__dict__ if latest_metrics else None
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting device status {device_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get device status")
