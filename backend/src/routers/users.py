"""
User management API endpoints
"""

import logging
from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from ..database import get_db_session
from ..models import User as UserModel


logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response models
class UserCreateRequest(BaseModel):
    name: str
    email: Optional[str] = None


class UserResponse(BaseModel):
    user_id: str
    name: str
    email: Optional[str]
    created_at: datetime


class UserListResponse(BaseModel):
    users: List[UserResponse]
    total: int


@router.get("/", response_model=UserListResponse)
async def get_users(
    skip: int = 0,
    limit: int = 100,
    db_session = Depends(get_db_session)
) -> UserListResponse:
    """Get all users"""
    try:
        users = db_session.query(UserModel).offset(skip).limit(limit).all()
        total = db_session.query(UserModel).count()
        
        user_responses = [
            UserResponse(
                user_id=user.user_id,
                name=user.name,
                email=user.email,
                created_at=user.created_at
            )
            for user in users
        ]
        
        return UserListResponse(users=user_responses, total=total)
    
    except Exception as e:
        logger.error(f"Error getting users: {e}")
        raise HTTPException(status_code=500, detail="Failed to get users")


@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: UserCreateRequest,
    db_session = Depends(get_db_session)
) -> UserResponse:
    """Create a new user"""
    try:
        # Generate user ID
        import uuid
        user_id = f"user_{uuid.uuid4().hex[:8]}"
        
        # Create user
        user = UserModel(
            user_id=user_id,
            name=user_data.name,
            email=user_data.email
        )
        
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        return UserResponse(
            user_id=user.user_id,
            name=user.name,
            email=user.email,
            created_at=user.created_at
        )
    
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(status_code=500, detail="Failed to create user")


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str,
    db_session = Depends(get_db_session)
) -> UserResponse:
    """Get user by ID"""
    try:
        user = db_session.query(UserModel).filter(UserModel.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return UserResponse(
            user_id=user.user_id,
            name=user.name,
            email=user.email,
            created_at=user.created_at
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get user")


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str,
    user_data: UserCreateRequest,
    db_session = Depends(get_db_session)
) -> UserResponse:
    """Update user"""
    try:
        user = db_session.query(UserModel).filter(UserModel.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        user.name = user_data.name
        if user_data.email is not None:
            user.email = user_data.email
        
        db_session.commit()
        db_session.refresh(user)
        
        return UserResponse(
            user_id=user.user_id,
            name=user.name,
            email=user.email,
            created_at=user.created_at
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update user")


@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    db_session = Depends(get_db_session)
) -> dict:
    """Delete user"""
    try:
        user = db_session.query(UserModel).filter(UserModel.user_id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        db_session.delete(user)
        db_session.commit()
        
        return {"message": f"User {user_id} deleted successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete user")
