"""
Data export API endpoints
"""

import logging
import csv
import json
from io import String<PERSON>
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from ..database import get_db_session
from ..models import DeviceData, Competition as CompetitionModel


logger = logging.getLogger(__name__)

router = APIRouter()


class ExportRequest(BaseModel):
    start_date: Optional[str] = None  # ISO format
    end_date: Optional[str] = None    # ISO format
    format: str = "csv"  # "csv" or "json"
    limit: int = 10000


@router.get("/competitions/{competition_id}")
async def export_competition_data(
    competition_id: str,
    format: str = "csv",
    db_session = Depends(get_db_session)
) -> StreamingResponse:
    """Export competition data"""
    try:
        # Check if competition exists
        competition = db_session.query(CompetitionModel).filter(
            CompetitionModel.competition_id == competition_id
        ).first()
        
        if not competition:
            raise HTTPException(status_code=404, detail="Competition not found")
        
        # Get competition data
        data_query = db_session.query(DeviceData).filter(
            DeviceData.competition_id == competition_id
        ).order_by(DeviceData.timestamp)
        
        data_records = data_query.all()
        
        if format.lower() == "csv":
            return _export_csv(data_records, f"competition_{competition_id}")
        elif format.lower() == "json":
            return _export_json(data_records, f"competition_{competition_id}")
        else:
            raise HTTPException(status_code=400, detail="Unsupported format")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting competition data: {e}")
        raise HTTPException(status_code=500, detail="Failed to export data")


@router.get("/devices/{device_id}/history")
async def export_device_history(
    device_id: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    format: str = "csv",
    limit: int = 10000,
    db_session = Depends(get_db_session)
) -> StreamingResponse:
    """Export device historical data"""
    try:
        # Build query
        query = db_session.query(DeviceData).filter(
            DeviceData.device_id == device_id
        )
        
        # Add date filters
        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            query = query.filter(DeviceData.timestamp >= start_dt)
        
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            query = query.filter(DeviceData.timestamp <= end_dt)
        
        # Apply limit and order
        data_records = query.order_by(DeviceData.timestamp.desc()).limit(limit).all()
        
        if format.lower() == "csv":
            return _export_csv(data_records, f"device_{device_id}_history")
        elif format.lower() == "json":
            return _export_json(data_records, f"device_{device_id}_history")
        else:
            raise HTTPException(status_code=400, detail="Unsupported format")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting device history: {e}")
        raise HTTPException(status_code=500, detail="Failed to export data")


@router.post("/custom")
async def export_custom_data(
    export_request: ExportRequest,
    db_session = Depends(get_db_session)
) -> StreamingResponse:
    """Export custom filtered data"""
    try:
        # Build query
        query = db_session.query(DeviceData)
        
        # Add date filters
        if export_request.start_date:
            start_dt = datetime.fromisoformat(export_request.start_date.replace('Z', '+00:00'))
            query = query.filter(DeviceData.timestamp >= start_dt)
        
        if export_request.end_date:
            end_dt = datetime.fromisoformat(export_request.end_date.replace('Z', '+00:00'))
            query = query.filter(DeviceData.timestamp <= end_dt)
        
        # Apply limit and order
        data_records = query.order_by(DeviceData.timestamp.desc()).limit(export_request.limit).all()
        
        if export_request.format.lower() == "csv":
            return _export_csv(data_records, "custom_export")
        elif export_request.format.lower() == "json":
            return _export_json(data_records, "custom_export")
        else:
            raise HTTPException(status_code=400, detail="Unsupported format")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting custom data: {e}")
        raise HTTPException(status_code=500, detail="Failed to export data")


def _export_csv(data_records, filename: str) -> StreamingResponse:
    """Export data as CSV"""
    output = StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow([
        "timestamp", "device_id", "user_id", "competition_id",
        "time", "distance", "stroke_rate", "power", "pace",
        "calories_per_hour", "total_calories", "heart_rate", "stroke_state"
    ])
    
    # Write data
    for record in data_records:
        writer.writerow([
            record.timestamp.isoformat() if record.timestamp else "",
            record.device_id or "",
            record.user_id or "",
            record.competition_id or "",
            record.time or 0,
            record.distance or 0,
            record.stroke_rate or 0,
            record.power or 0,
            record.pace or 0,
            record.calories_per_hour or 0,
            record.total_calories or 0,
            record.heart_rate or 0,
            record.stroke_state or 0
        ])
    
    output.seek(0)
    
    return StreamingResponse(
        iter([output.getvalue()]),
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}.csv"}
    )


def _export_json(data_records, filename: str) -> StreamingResponse:
    """Export data as JSON"""
    data = []
    
    for record in data_records:
        data.append({
            "timestamp": record.timestamp.isoformat() if record.timestamp else None,
            "device_id": record.device_id,
            "user_id": record.user_id,
            "competition_id": record.competition_id,
            "metrics": {
                "time": record.time,
                "distance": record.distance,
                "stroke_rate": record.stroke_rate,
                "power": record.power,
                "pace": record.pace,
                "calories_per_hour": record.calories_per_hour,
                "total_calories": record.total_calories,
                "heart_rate": record.heart_rate,
                "stroke_state": record.stroke_state
            }
        })
    
    json_data = json.dumps(data, indent=2)
    
    return StreamingResponse(
        iter([json_data]),
        media_type="application/json",
        headers={"Content-Disposition": f"attachment; filename={filename}.json"}
    )
