"""
Competition management API endpoints
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel

from ..database import get_db_session
from ..models import Competition as CompetitionModel
from ..competition import CompetitionManager


logger = logging.getLogger(__name__)

router = APIRouter()


# Request/Response models
class CompetitionCreateRequest(BaseModel):
    name: str
    type: str  # "distance", "time", "interval", "free"
    target: Optional[float] = None  # distance in meters or time in seconds
    duration: Optional[int] = None  # max duration in seconds
    participants: List[str] = []  # user_ids


class CompetitionResponse(BaseModel):
    competition_id: str
    name: str
    type: str
    status: str
    target: Optional[float]
    duration: Optional[int]
    participants: List[str]
    created_at: datetime
    started_at: Optional[datetime]
    finished_at: Optional[datetime]


class CompetitionListResponse(BaseModel):
    competitions: List[CompetitionResponse]
    total: int


class CompetitionStartRequest(BaseModel):
    countdown: int = 10


def get_competition_manager(request: Request) -> CompetitionManager:
    """Dependency to get competition manager"""
    if not hasattr(request.app.state, 'competition_manager'):
        # Initialize competition manager if not exists
        from ..competition import CompetitionManager
        request.app.state.competition_manager = CompetitionManager(
            pm5_manager=request.app.state.pm5_manager,
            ws_manager=request.app.state.ws_manager
        )
    return request.app.state.competition_manager


@router.get("/", response_model=CompetitionListResponse)
async def get_competitions(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db_session = Depends(get_db_session)
) -> CompetitionListResponse:
    """Get all competitions"""
    try:
        query = db_session.query(CompetitionModel)
        
        if status:
            query = query.filter(CompetitionModel.status == status)
        
        competitions = query.offset(skip).limit(limit).all()
        total = query.count()
        
        competition_responses = [
            CompetitionResponse(
                competition_id=comp.competition_id,
                name=comp.name,
                type=comp.type,
                status=comp.status,
                target=comp.target,
                duration=comp.duration,
                participants=comp.participants or [],
                created_at=comp.created_at,
                started_at=comp.started_at,
                finished_at=comp.finished_at
            )
            for comp in competitions
        ]
        
        return CompetitionListResponse(competitions=competition_responses, total=total)
    
    except Exception as e:
        logger.error(f"Error getting competitions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get competitions")


@router.post("/", response_model=CompetitionResponse)
async def create_competition(
    competition_data: CompetitionCreateRequest,
    competition_manager: CompetitionManager = Depends(get_competition_manager),
    db_session = Depends(get_db_session)
) -> CompetitionResponse:
    """Create a new competition"""
    try:
        # Generate competition ID
        import uuid
        competition_id = f"comp_{uuid.uuid4().hex[:8]}"
        
        # Create competition
        competition = await competition_manager.create_competition(
            competition_id=competition_id,
            name=competition_data.name,
            competition_type=competition_data.type,
            target=competition_data.target,
            duration=competition_data.duration,
            participants=competition_data.participants
        )
        
        return CompetitionResponse(
            competition_id=competition.competition_id,
            name=competition.name,
            type=competition.type.value,
            status=competition.status.value,
            target=competition.target,
            duration=competition.duration,
            participants=competition.participants,
            created_at=competition.created_at,
            started_at=competition.started_at,
            finished_at=competition.finished_at
        )
    
    except Exception as e:
        logger.error(f"Error creating competition: {e}")
        raise HTTPException(status_code=500, detail="Failed to create competition")


@router.get("/{competition_id}", response_model=CompetitionResponse)
async def get_competition(
    competition_id: str,
    competition_manager: CompetitionManager = Depends(get_competition_manager)
) -> CompetitionResponse:
    """Get competition by ID"""
    try:
        competition = competition_manager.get_competition(competition_id)
        if not competition:
            raise HTTPException(status_code=404, detail="Competition not found")
        
        return CompetitionResponse(
            competition_id=competition.competition_id,
            name=competition.name,
            type=competition.type.value,
            status=competition.status.value,
            target=competition.target,
            duration=competition.duration,
            participants=competition.participants,
            created_at=competition.created_at,
            started_at=competition.started_at,
            finished_at=competition.finished_at
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting competition {competition_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get competition")


@router.post("/{competition_id}/start")
async def start_competition(
    competition_id: str,
    start_request: CompetitionStartRequest,
    competition_manager: CompetitionManager = Depends(get_competition_manager)
) -> Dict[str, str]:
    """Start a competition"""
    try:
        success = await competition_manager.start_competition(
            competition_id, 
            countdown=start_request.countdown
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to start competition")
        
        return {"message": f"Competition {competition_id} started with {start_request.countdown}s countdown"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting competition {competition_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to start competition")


@router.post("/{competition_id}/stop")
async def stop_competition(
    competition_id: str,
    competition_manager: CompetitionManager = Depends(get_competition_manager)
) -> Dict[str, str]:
    """Stop a competition"""
    try:
        success = await competition_manager.stop_competition(competition_id)
        
        if not success:
            raise HTTPException(status_code=400, detail="Failed to stop competition")
        
        return {"message": f"Competition {competition_id} stopped"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping competition {competition_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to stop competition")


@router.get("/{competition_id}/leaderboard")
async def get_leaderboard(
    competition_id: str,
    competition_manager: CompetitionManager = Depends(get_competition_manager)
) -> Dict[str, Any]:
    """Get current leaderboard for competition"""
    try:
        leaderboard = await competition_manager.get_leaderboard(competition_id)
        
        if leaderboard is None:
            raise HTTPException(status_code=404, detail="Competition not found")
        
        return {
            "competition_id": competition_id,
            "leaderboard": leaderboard,
            "updated_at": datetime.now()
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting leaderboard for {competition_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to get leaderboard")
