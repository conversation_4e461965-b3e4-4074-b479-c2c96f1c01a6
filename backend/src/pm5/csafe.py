"""
CSAFE Protocol Implementation for PM5 Communication

Based on the CSAFE (Communications Specification for Fitness Equipment) protocol
and Concept2's PM5 CSAFE Communication Definition.
"""

import struct
import logging
from typing import List, Dict, Any, Optional, Tuple
from enum import IntEnum
from dataclasses import dataclass


logger = logging.getLogger(__name__)


class CSAFECommand(IntEnum):
    """CSAFE command codes"""
    # Status commands
    GETSTATUS = 0x80
    RESET = 0x81
    GOIDLE = 0x82
    GOHAVEID = 0x83
    GOINUSE = 0x85
    GOFINISHED = 0x86
    GOREADY = 0x87
    BADID = 0x88
    
    # Configuration commands
    GETVERSION = 0x91
    GETID = 0x92
    GETUNITS = 0x93
    GETSERIAL = 0x94
    
    # Data commands
    GETODOMETER = 0x9B
    GETERRORVALUE = 0x9C
    GETWORKTIME = 0xA0
    GETWORKDISTANCE = 0xA1
    GETWORKOUTTYPE = 0x89
    GETWORKOUTSTATE = 0x8D
    
    # PM5 specific commands
    PM_GET_WORKOUTTYPE = 0x1A
    PM_GET_DRAGFACTOR = 0x6A
    PM_GET_STROKESTATE = 0x6F
    PM_GET_WORKTIME = 0xA0
    PM_GET_WORKDISTANCE = 0xA1
    PM_GET_ERRORVALUE = 0x9C
    PM_GET_WORKOUTSTATE = 0x8D
    PM_GET_INTERVALTYPE = 0x8E
    PM_GET_RESTTIME = 0x8F
    
    # Force plot and stroke data
    PM_GET_FORCEPLOTDATA = 0x6B
    PM_GET_HEARTBEATDATA = 0x6C


class CSAFEStatus(IntEnum):
    """CSAFE status codes"""
    ERROR = 0
    READY = 1
    IDLE = 2
    HAVEID = 3
    INUSE = 5
    PAUSE = 6
    FINISHED = 7
    MANUAL = 8
    OFFLINE = 9


@dataclass
class CSAFEFrame:
    """CSAFE communication frame"""
    start_flag: int = 0xF1
    dest_address: int = 0x00
    source_address: int = 0x00
    data_length: int = 0
    data: bytes = b''
    checksum: int = 0
    stop_flag: int = 0xF2


class CSAFEProtocol:
    """CSAFE protocol implementation for PM5 communication"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def create_frame(self, commands: List[int]) -> bytes:
        """Create a CSAFE frame from command list"""
        try:
            # Convert commands to bytes
            data = bytes(commands)
            
            # Create frame
            frame = CSAFEFrame(
                data_length=len(data),
                data=data
            )
            
            # Calculate checksum
            frame.checksum = self._calculate_checksum(frame)
            
            # Pack frame into bytes
            frame_bytes = struct.pack(
                'BBBB',
                frame.start_flag,
                frame.dest_address,
                frame.source_address,
                frame.data_length
            )
            frame_bytes += frame.data
            frame_bytes += struct.pack('BB', frame.checksum, frame.stop_flag)
            
            return frame_bytes
        
        except Exception as e:
            self.logger.error(f"Error creating CSAFE frame: {e}")
            return b''
    
    def parse_frame(self, data: bytes) -> Optional[CSAFEFrame]:
        """Parse received CSAFE frame"""
        try:
            if len(data) < 6:  # Minimum frame size
                return None
            
            # Check start flag
            if data[0] != 0xF1:
                return None
            
            # Parse header
            dest_addr = data[1]
            source_addr = data[2]
            data_length = data[3]
            
            # Check if we have enough data
            if len(data) < 6 + data_length:
                return None
            
            # Extract frame data
            frame_data = data[4:4+data_length]
            checksum = data[4+data_length]
            stop_flag = data[5+data_length]
            
            # Check stop flag
            if stop_flag != 0xF2:
                return None
            
            # Create frame object
            frame = CSAFEFrame(
                dest_address=dest_addr,
                source_address=source_addr,
                data_length=data_length,
                data=frame_data,
                checksum=checksum
            )
            
            # Verify checksum
            calculated_checksum = self._calculate_checksum(frame)
            if calculated_checksum != checksum:
                self.logger.warning(f"Checksum mismatch: {calculated_checksum} != {checksum}")
                return None
            
            return frame
        
        except Exception as e:
            self.logger.error(f"Error parsing CSAFE frame: {e}")
            return None
    
    def _calculate_checksum(self, frame: CSAFEFrame) -> int:
        """Calculate CSAFE checksum"""
        checksum = 0
        checksum ^= frame.dest_address
        checksum ^= frame.source_address
        checksum ^= frame.data_length
        
        for byte in frame.data:
            checksum ^= byte
        
        return checksum & 0xFF
    
    def create_get_status_command(self) -> bytes:
        """Create get status command"""
        return self.create_frame([CSAFECommand.GETSTATUS])
    
    def create_get_monitor_command(self) -> bytes:
        """Create command to get monitor data"""
        commands = [
            CSAFECommand.PM_GET_WORKTIME,
            CSAFECommand.PM_GET_WORKDISTANCE,
            CSAFECommand.GETODOMETER,
            CSAFECommand.PM_GET_STROKESTATE
        ]
        return self.create_frame(commands)
    
    def create_get_forceplot_command(self) -> bytes:
        """Create command to get force plot data"""
        return self.create_frame([CSAFECommand.PM_GET_FORCEPLOTDATA])
    
    def create_get_device_info_command(self) -> bytes:
        """Create command to get device information"""
        commands = [
            CSAFECommand.GETVERSION,
            CSAFECommand.GETSERIAL,
            CSAFECommand.GETID
        ]
        return self.create_frame(commands)
    
    def parse_status_response(self, frame: CSAFEFrame) -> Optional[int]:
        """Parse status response"""
        try:
            if len(frame.data) >= 2:
                # Status is typically in the second byte
                return frame.data[1]
            return None
        except Exception as e:
            self.logger.error(f"Error parsing status response: {e}")
            return None
    
    def parse_monitor_response(self, frame: CSAFEFrame) -> Optional[Dict[str, Any]]:
        """Parse monitor data response"""
        try:
            data = {}
            pos = 0
            
            while pos < len(frame.data):
                if pos + 1 >= len(frame.data):
                    break
                
                cmd = frame.data[pos]
                length = frame.data[pos + 1] if pos + 1 < len(frame.data) else 0
                pos += 2
                
                if pos + length > len(frame.data):
                    break
                
                value_bytes = frame.data[pos:pos + length]
                pos += length
                
                # Parse based on command
                if cmd == CSAFECommand.PM_GET_WORKTIME and length >= 4:
                    # Work time in centiseconds
                    data['time'] = struct.unpack('<I', value_bytes[:4])[0] / 100.0
                
                elif cmd == CSAFECommand.PM_GET_WORKDISTANCE and length >= 4:
                    # Work distance in meters
                    data['distance'] = struct.unpack('<I', value_bytes[:4])[0]
                
                elif cmd == CSAFECommand.GETODOMETER and length >= 4:
                    # Odometer data
                    data['odometer'] = struct.unpack('<I', value_bytes[:4])[0]
                
                elif cmd == CSAFECommand.PM_GET_STROKESTATE and length >= 1:
                    # Stroke state
                    data['stroke_state'] = value_bytes[0]
            
            return data if data else None
        
        except Exception as e:
            self.logger.error(f"Error parsing monitor response: {e}")
            return None
    
    def parse_forceplot_response(self, frame: CSAFEFrame) -> Optional[List[int]]:
        """Parse force plot response"""
        try:
            if len(frame.data) < 3:
                return None
            
            # Skip command and length bytes
            force_data = frame.data[2:]
            
            # Force plot data is typically 16-bit values
            force_plot = []
            for i in range(0, len(force_data), 2):
                if i + 1 < len(force_data):
                    value = struct.unpack('<H', force_data[i:i+2])[0]
                    force_plot.append(value)
            
            return force_plot
        
        except Exception as e:
            self.logger.error(f"Error parsing force plot response: {e}")
            return None
    
    def parse_device_info_response(self, frame: CSAFEFrame) -> Optional[Dict[str, str]]:
        """Parse device information response"""
        try:
            info = {}
            pos = 0
            
            while pos < len(frame.data):
                if pos + 1 >= len(frame.data):
                    break
                
                cmd = frame.data[pos]
                length = frame.data[pos + 1] if pos + 1 < len(frame.data) else 0
                pos += 2
                
                if pos + length > len(frame.data):
                    break
                
                value_bytes = frame.data[pos:pos + length]
                pos += length
                
                # Parse based on command
                if cmd == CSAFECommand.GETVERSION:
                    # Version information
                    if length >= 2:
                        major = value_bytes[0]
                        minor = value_bytes[1]
                        info['firmware_version'] = f"{major}.{minor}"
                
                elif cmd == CSAFECommand.GETSERIAL:
                    # Serial number
                    info['serial_number'] = value_bytes.decode('ascii', errors='ignore').strip()
                
                elif cmd == CSAFECommand.GETID:
                    # Device ID
                    if length >= 5:
                        mfg_id = struct.unpack('<H', value_bytes[:2])[0]
                        model = struct.unpack('<H', value_bytes[2:4])[0]
                        info['manufacturer_id'] = str(mfg_id)
                        info['model'] = f"PM{model}" if model == 5 else f"Model_{model}"
            
            return info if info else None
        
        except Exception as e:
            self.logger.error(f"Error parsing device info response: {e}")
            return None
