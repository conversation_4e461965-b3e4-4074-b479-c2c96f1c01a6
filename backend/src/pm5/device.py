"""
PM5 Device Communication Class

Handles individual PM5 device connections and data polling.
"""

import asyncio
import logging
import serial
import serial.tools.list_ports
from datetime import datetime
from typing import Optional, Dict, Any, Callable, List
from dataclasses import dataclass

from .csafe import CSAFEProtocol, CSAFEStatus
from ..config import settings


logger = logging.getLogger(__name__)


# Import data models from shared protocols
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'shared', 'protocols'))
from data_models import Device, DeviceInfo, DeviceStatus, PM5Metrics, StrokeState


@dataclass
class ConnectionConfig:
    """Connection configuration for PM5 device"""
    port: str
    baudrate: int = 9600
    timeout: float = 1.0
    connection_type: str = "usb"


class PM5Device:
    """Individual PM5 device communication handler"""
    
    def __init__(self, device_id: str, connection_config: ConnectionConfig):
        self.device_id = device_id
        self.connection_config = connection_config
        self.csafe = CSAFEProtocol()
        self.logger = logging.getLogger(f"{__name__}.{device_id}")
        
        # Connection state
        self.serial_connection: Optional[serial.Serial] = None
        self.is_connected = False
        self.last_seen = datetime.now()
        
        # Device information
        self.device_info: Optional[DeviceInfo] = None
        self.current_user_id: Optional[str] = None
        self.status = DeviceStatus.DISCONNECTED
        
        # Data polling
        self.polling_task: Optional[asyncio.Task] = None
        self.data_callback: Optional[Callable] = None
        self.latest_metrics: Optional[PM5Metrics] = None
        
        # Error handling
        self.connection_errors = 0
        self.max_connection_errors = 5
    
    async def connect(self) -> bool:
        """Connect to the PM5 device"""
        try:
            self.logger.info(f"Connecting to PM5 device on {self.connection_config.port}")
            
            # Open serial connection
            self.serial_connection = serial.Serial(
                port=self.connection_config.port,
                baudrate=self.connection_config.baudrate,
                timeout=self.connection_config.timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            
            # Test connection with status command
            if await self._test_connection():
                self.is_connected = True
                self.status = DeviceStatus.CONNECTED
                self.connection_errors = 0
                self.last_seen = datetime.now()
                
                # Get device information
                await self._get_device_info()
                
                self.logger.info(f"Successfully connected to PM5 device {self.device_id}")
                return True
            else:
                await self.disconnect()
                return False
        
        except Exception as e:
            self.logger.error(f"Failed to connect to PM5 device {self.device_id}: {e}")
            await self.disconnect()
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from the PM5 device"""
        try:
            self.is_connected = False
            self.status = DeviceStatus.DISCONNECTED
            
            # Stop polling
            if self.polling_task and not self.polling_task.done():
                self.polling_task.cancel()
                try:
                    await self.polling_task
                except asyncio.CancelledError:
                    pass
            
            # Close serial connection
            if self.serial_connection and self.serial_connection.is_open:
                self.serial_connection.close()
                self.serial_connection = None
            
            self.logger.info(f"Disconnected from PM5 device {self.device_id}")
        
        except Exception as e:
            self.logger.error(f"Error disconnecting from PM5 device {self.device_id}: {e}")
    
    async def start_polling(self, data_callback: Callable) -> None:
        """Start data polling from the device"""
        if not self.is_connected:
            raise RuntimeError("Device not connected")
        
        self.data_callback = data_callback
        self.polling_task = asyncio.create_task(self._polling_loop())
        self.logger.info(f"Started data polling for PM5 device {self.device_id}")
    
    async def stop_polling(self) -> None:
        """Stop data polling"""
        if self.polling_task and not self.polling_task.done():
            self.polling_task.cancel()
            try:
                await self.polling_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info(f"Stopped data polling for PM5 device {self.device_id}")
    
    async def _test_connection(self) -> bool:
        """Test connection with a simple status command"""
        try:
            command = self.csafe.create_get_status_command()
            response = await self._send_command(command)
            
            if response:
                frame = self.csafe.parse_frame(response)
                if frame:
                    status = self.csafe.parse_status_response(frame)
                    return status is not None
            
            return False
        
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False
    
    async def _get_device_info(self) -> None:
        """Get device information"""
        try:
            command = self.csafe.create_get_device_info_command()
            response = await self._send_command(command)
            
            if response:
                frame = self.csafe.parse_frame(response)
                if frame:
                    info = self.csafe.parse_device_info_response(frame)
                    if info:
                        self.device_info = DeviceInfo(
                            model=info.get('model', 'PM5'),
                            serial_number=info.get('serial_number', 'Unknown'),
                            firmware_version=info.get('firmware_version', 'Unknown'),
                            hardware_version=info.get('hardware_version')
                        )
                        self.logger.info(f"Device info: {self.device_info}")
        
        except Exception as e:
            self.logger.error(f"Failed to get device info: {e}")
    
    async def _polling_loop(self) -> None:
        """Main data polling loop"""
        self.logger.info(f"Starting polling loop for device {self.device_id}")
        
        while self.is_connected:
            try:
                # Get monitor data
                metrics = await self._get_monitor_data()
                
                if metrics:
                    self.latest_metrics = metrics
                    self.last_seen = datetime.now()
                    self.connection_errors = 0
                    
                    # Call data callback
                    if self.data_callback:
                        await self.data_callback(self.device_id, metrics)
                else:
                    self.connection_errors += 1
                    if self.connection_errors >= self.max_connection_errors:
                        self.logger.error(f"Too many connection errors, disconnecting device {self.device_id}")
                        self.status = DeviceStatus.ERROR
                        break
                
                # Wait for next poll
                await asyncio.sleep(settings.pm5_poll_interval)
            
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in polling loop: {e}")
                self.connection_errors += 1
                if self.connection_errors >= self.max_connection_errors:
                    self.status = DeviceStatus.ERROR
                    break
                await asyncio.sleep(1.0)  # Wait before retry
        
        self.logger.info(f"Polling loop ended for device {self.device_id}")
    
    async def _get_monitor_data(self) -> Optional[PM5Metrics]:
        """Get current monitor data from device"""
        try:
            command = self.csafe.create_get_monitor_command()
            response = await self._send_command(command)
            
            if response:
                frame = self.csafe.parse_frame(response)
                if frame:
                    data = self.csafe.parse_monitor_response(frame)
                    if data:
                        # Convert to PM5Metrics
                        metrics = PM5Metrics(
                            time=data.get('time', 0.0),
                            distance=data.get('distance', 0.0),
                            stroke_rate=data.get('stroke_rate', 0),
                            power=data.get('power', 0),
                            pace=data.get('pace', 0.0),
                            calories_per_hour=data.get('calories_per_hour', 0),
                            total_calories=data.get('total_calories', 0),
                            heart_rate=data.get('heart_rate'),
                            stroke_state=StrokeState(data.get('stroke_state', 0))
                        )
                        
                        # Get force plot data if available
                        force_plot = await self._get_force_plot()
                        if force_plot:
                            metrics.force_plot = force_plot
                        
                        return metrics
            
            return None
        
        except Exception as e:
            self.logger.error(f"Failed to get monitor data: {e}")
            return None
    
    async def _get_force_plot(self) -> Optional[List[int]]:
        """Get force plot data"""
        try:
            command = self.csafe.create_get_forceplot_command()
            response = await self._send_command(command)
            
            if response:
                frame = self.csafe.parse_frame(response)
                if frame:
                    return self.csafe.parse_forceplot_response(frame)
            
            return None
        
        except Exception as e:
            self.logger.debug(f"Failed to get force plot: {e}")  # Debug level since this is optional
            return None
    
    async def _send_command(self, command: bytes) -> Optional[bytes]:
        """Send command to device and get response"""
        try:
            if not self.serial_connection or not self.serial_connection.is_open:
                return None
            
            # Send command
            self.serial_connection.write(command)
            self.serial_connection.flush()
            
            # Wait for response
            await asyncio.sleep(0.01)  # Small delay for device processing
            
            # Read response
            if self.serial_connection.in_waiting > 0:
                response = self.serial_connection.read(self.serial_connection.in_waiting)
                return response
            
            return None
        
        except Exception as e:
            self.logger.error(f"Failed to send command: {e}")
            return None
    
    def get_device_data(self) -> Device:
        """Get current device data"""
        return Device(
            device_id=self.device_id,
            status=self.status,
            last_seen=self.last_seen,
            device_info=self.device_info or DeviceInfo(
                model="PM5",
                serial_number="Unknown",
                firmware_version="Unknown"
            ),
            current_user_id=self.current_user_id,
            connection_type=self.connection_config.connection_type
        )
    
    def set_user(self, user_id: str) -> None:
        """Set current user for this device"""
        self.current_user_id = user_id
        self.logger.info(f"Device {self.device_id} assigned to user {user_id}")
    
    def clear_user(self) -> None:
        """Clear current user from this device"""
        old_user = self.current_user_id
        self.current_user_id = None
        self.logger.info(f"Device {self.device_id} unassigned from user {old_user}")


def discover_pm5_devices() -> List[ConnectionConfig]:
    """Discover available PM5 devices on USB ports"""
    devices = []
    
    try:
        # List all available serial ports
        ports = serial.tools.list_ports.comports()
        
        for port in ports:
            # Check if this could be a PM5 device
            # PM5 devices typically appear as USB serial devices
            if port.vid and port.pid:
                # Concept2 vendor ID is 0x17A4
                if port.vid == 0x17A4:
                    config = ConnectionConfig(
                        port=port.device,
                        connection_type="usb"
                    )
                    devices.append(config)
                    logger.info(f"Found potential PM5 device on {port.device}")
    
    except Exception as e:
        logger.error(f"Error discovering PM5 devices: {e}")
    
    return devices
