"""
PM5 Communication Module

This module handles communication with Concept2 PM5 ergometers using the CSAFE protocol.
It supports both USB serial and Bluetooth Low Energy connections.
"""

from .manager import PM5Manager
from .device import PM5Device
from .csafe import CSAFEProtocol
from .simulator import PM5Simulator

__all__ = [
    "PM5Manager",
    "PM5Device", 
    "CSAFEProtocol",
    "PM5Simulator"
]
