"""
Main FastAPI application for PM5 Competition Management System
"""

import logging
import logging.config
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from .config import settings, get_log_config
from .database import init_database, close_database
from .pm5 import PM5Manager
from .websocket import WebSocketManager
from .routers import devices, users, competitions, data_export


# Configure logging
logging.config.dictConfig(get_log_config())
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting PM5 Competition Management System")
    
    # Initialize database
    await init_database()
    logger.info("Database initialized")
    
    # Initialize PM5 manager
    pm5_manager = PM5Manager()
    app.state.pm5_manager = pm5_manager
    await pm5_manager.start()
    logger.info("PM5 Manager started")
    
    # Initialize WebSocket manager
    ws_manager = WebSocketManager()
    app.state.ws_manager = ws_manager
    logger.info("WebSocket Manager initialized")
    
    # Connect PM5 manager to WebSocket manager
    pm5_manager.set_websocket_manager(ws_manager)
    
    yield
    
    # Cleanup
    logger.info("Shutting down PM5 Competition Management System")
    await pm5_manager.stop()
    await close_database()
    logger.info("Shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="PM5 Competition Management System",
    description="Backend API for managing Concept2 PM5 ergometer competitions",
    version="0.1.0",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)


# Health check endpoint
@app.get("/health")
async def health_check() -> Dict[str, Any]:
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "0.1.0",
        "debug": settings.debug,
        "pm5_devices": len(app.state.pm5_manager.devices) if hasattr(app.state, 'pm5_manager') else 0,
        "websocket_connections": app.state.ws_manager.connection_count if hasattr(app.state, 'ws_manager') else 0
    }


# Root endpoint
@app.get("/")
async def root() -> Dict[str, str]:
    """Root endpoint"""
    return {
        "message": "PM5 Competition Management System API",
        "docs": "/docs" if settings.debug else "Documentation disabled in production",
        "health": "/health"
    }


# Include routers
app.include_router(
    devices.router,
    prefix="/api/v1/devices",
    tags=["devices"]
)

app.include_router(
    users.router,
    prefix="/api/v1/users",
    tags=["users"]
)

app.include_router(
    competitions.router,
    prefix="/api/v1/competitions",
    tags=["competitions"]
)

app.include_router(
    data_export.router,
    prefix="/api/v1/export",
    tags=["export"]
)

# Include WebSocket router
from .websocket import router as websocket_router
app.include_router(websocket_router)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    if settings.debug:
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": str(exc),
                    "type": type(exc).__name__
                }
            }
        )
    else:
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "An internal server error occurred"
                }
            }
        )


# HTTP exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": "HTTP_ERROR",
                "message": exc.detail,
                "status_code": exc.status_code
            }
        }
    )


if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower()
    )
