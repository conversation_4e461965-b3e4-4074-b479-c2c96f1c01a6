"""
Configuration management for the PM5 Competition Backend
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator
from pathlib import Path


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # Application
    debug: bool = False
    log_level: str = "INFO"
    
    # Server
    host: str = "localhost"
    port: int = 8080
    reload: bool = False
    
    # Database
    database_url: str = "sqlite:///./competition_data.db"
    
    # PM5 Communication
    pm5_poll_interval: float = 0.1  # 10Hz
    pm5_connection_timeout: float = 5.0
    pm5_max_devices: int = 10
    
    # WebSocket
    ws_heartbeat_interval: int = 30
    ws_max_connections: int = 50
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS
    allowed_origins: List[str] = ["http://localhost:3000", "http://localhost:3001"]
    allowed_methods: List[str] = ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: List[str] = ["*"]
    
    # Unity
    unity_api_key: str = "unity-api-key-change-in-production"
    unity_ws_endpoint: str = "/ws/unity"
    
    # Admin Frontend
    admin_ws_endpoint: str = "/ws/admin"
    
    # Competition
    max_competition_duration: int = 3600  # 1 hour
    max_participants_per_competition: int = 20
    default_countdown_duration: int = 10
    
    # Data Export
    export_format: str = "csv"
    export_max_records: int = 10000
    
    # Monitoring
    enable_metrics: bool = True
    metrics_endpoint: str = "/metrics"
    
    @validator("allowed_origins", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("allowed_methods", pre=True)
    def parse_cors_methods(cls, v):
        if isinstance(v, str):
            return [method.strip() for method in v.split(",")]
        return v
    
    @validator("allowed_headers", pre=True)
    def parse_cors_headers(cls, v):
        if isinstance(v, str):
            return [header.strip() for header in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_database_path() -> Path:
    """Get the absolute path to the database file"""
    if settings.database_url.startswith("sqlite:///"):
        db_path = settings.database_url.replace("sqlite:///", "")
        if not os.path.isabs(db_path):
            # Make relative to backend directory
            backend_dir = Path(__file__).parent.parent
            return backend_dir / db_path
        return Path(db_path)
    return None


def get_log_config() -> dict:
    """Get logging configuration"""
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
            "file": {
                "formatter": "detailed",
                "class": "logging.FileHandler",
                "filename": "backend.log",
            },
        },
        "root": {
            "level": settings.log_level,
            "handlers": ["default", "file"] if not settings.debug else ["default"],
        },
        "loggers": {
            "uvicorn": {
                "level": "INFO",
                "handlers": ["default"],
                "propagate": False,
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": ["default"],
                "propagate": False,
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["default"],
                "propagate": False,
            },
        },
    }


def validate_settings() -> None:
    """Validate critical settings"""
    if settings.secret_key == "your-secret-key-change-in-production":
        if not settings.debug:
            raise ValueError("SECRET_KEY must be changed in production")
    
    if settings.unity_api_key == "unity-api-key-change-in-production":
        if not settings.debug:
            raise ValueError("UNITY_API_KEY must be changed in production")
    
    if settings.pm5_poll_interval <= 0:
        raise ValueError("PM5_POLL_INTERVAL must be positive")
    
    if settings.pm5_max_devices <= 0:
        raise ValueError("PM5_MAX_DEVICES must be positive")


# Validate settings on import
validate_settings()
