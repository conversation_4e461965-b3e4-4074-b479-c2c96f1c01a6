# Concept2 PM5 Competition Management System

A comprehensive three-component system for managing Concept2 PM5 ergometer competitions with real-time visualization and administrative oversight.

## System Architecture

### Component 1: Python Backend (`backend/`)
- **Purpose**: Connect to PM5 devices, process data, and provide APIs
- **Features**:
  - Bluetooth/USB communication with PM5 ergometers
  - Real-time data processing and storage
  - WebSocket and REST API endpoints
  - Device registration and management

### Component 2: Unity Visualization (`unity-app/`)
- **Purpose**: Real-time competition visualization across multiple displays
- **Features**:
  - Multi-screen support (1-5 displays)
  - Real-time user position tracking
  - Competition management interface
  - Dynamic leaderboards and statistics

### Component 3: Admin Frontend (`admin-frontend/`)
- **Purpose**: Administrative dashboard for system management
- **Features**:
  - Device monitoring and status
  - User registration and management
  - Competition configuration
  - Data export and reporting

## Technical Stack

- **Backend**: Python 3.9+, FastAPI, WebSockets, SQLite
- **Unity**: Unity 2022.3 LTS, C#
- **Frontend**: React/Next.js, TypeScript
- **Platform**: Mac OS X M1 compatible
- **Communication**: WebSocket for real-time data, REST APIs for configuration

## Getting Started

1. **Backend Setup**:
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   python main.py
   ```

2. **Unity Application**:
   - Open `unity-app/` in Unity 2022.3 LTS
   - Build and run for Mac OS X

3. **Admin Frontend**:
   ```bash
   cd admin-frontend
   npm install
   npm run dev
   ```

## Hardware Requirements

- Mac OS X M1 computer
- Concept2 PM5 ergometers
- Multiple displays (optional, 1-5 supported)
- Bluetooth or USB connectivity

## Development Status

- [ ] Project Setup and Architecture
- [ ] Python Backend Development
- [ ] Unity Visualization Application
- [ ] Admin Frontend Development
- [ ] Integration and Testing

## License

MIT License - See LICENSE file for details
