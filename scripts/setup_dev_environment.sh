#!/bin/bash

# Setup Development Environment for PM5 Competition Management System
# Compatible with Mac OS X M1

set -e

echo "🚀 Setting up PM5 Competition Management System Development Environment"
echo "Platform: Mac OS X M1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on Mac OS X
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for Mac OS X. Current OS: $OSTYPE"
    exit 1
fi

# Check if running on Apple Silicon
if [[ $(uname -m) != "arm64" ]]; then
    print_warning "This script is optimized for Apple Silicon (M1/M2). Current architecture: $(uname -m)"
fi

# Check for Homebrew
print_status "Checking for Homebrew..."
if ! command -v brew &> /dev/null; then
    print_status "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
else
    print_success "Homebrew is already installed"
fi

# Update Homebrew
print_status "Updating Homebrew..."
brew update

# Install Python 3.9+ if not present
print_status "Checking Python installation..."
if ! command -v python3 &> /dev/null || [[ $(python3 -c 'import sys; print(sys.version_info >= (3, 9))') != "True" ]]; then
    print_status "Installing Python 3.11..."
    brew install python@3.11
    echo 'export PATH="/opt/homebrew/bin:$PATH"' >> ~/.zshrc
else
    print_success "Python 3.9+ is already installed: $(python3 --version)"
fi

# Install libusb for PM5 USB communication
print_status "Installing libusb for PM5 communication..."
brew install libusb

# Install Node.js for admin frontend
print_status "Installing Node.js..."
if ! command -v node &> /dev/null; then
    brew install node
else
    print_success "Node.js is already installed: $(node --version)"
fi

# Install Unity Hub (if not present)
print_status "Checking for Unity Hub..."
if ! ls /Applications/Unity\ Hub.app &> /dev/null; then
    print_warning "Unity Hub not found. Please install Unity Hub manually from:"
    print_warning "https://unity3d.com/get-unity/download"
    print_warning "After installation, install Unity 2022.3 LTS"
else
    print_success "Unity Hub is installed"
fi

# Setup Python Backend
print_status "Setting up Python backend environment..."
cd backend

# Create virtual environment
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
else
    print_success "Virtual environment already exists"
fi

# Activate virtual environment and install dependencies
print_status "Installing Python dependencies..."
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# Copy environment file
if [ ! -f ".env" ]; then
    print_status "Creating environment configuration..."
    cp .env.example .env
    print_warning "Please edit backend/.env with your specific configuration"
else
    print_success "Environment file already exists"
fi

cd ..

# Setup Admin Frontend
print_status "Setting up admin frontend..."
cd admin-frontend

# Create package.json for React/Next.js
if [ ! -f "package.json" ]; then
    print_status "Initializing admin frontend..."
    cat > package.json << EOF
{
  "name": "pm5-admin-frontend",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev -p 3000",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "14.0.3",
    "react": "18.2.0",
    "react-dom": "18.2.0",
    "typescript": "5.3.2",
    "@types/node": "20.9.0",
    "@types/react": "18.2.37",
    "@types/react-dom": "18.2.15",
    "tailwindcss": "3.3.5",
    "autoprefixer": "10.4.16",
    "postcss": "8.4.31",
    "axios": "1.6.1",
    "socket.io-client": "4.7.4",
    "recharts": "2.8.0",
    "lucide-react": "0.292.0"
  },
  "devDependencies": {
    "eslint": "8.54.0",
    "eslint-config-next": "14.0.3"
  }
}
EOF
    print_status "Installing frontend dependencies..."
    npm install
else
    print_success "Admin frontend already initialized"
fi

cd ..

# Create Unity project structure
print_status "Setting up Unity project structure..."
mkdir -p unity-app/Assets/Scripts
mkdir -p unity-app/Assets/Scenes
mkdir -p unity-app/Assets/Prefabs
mkdir -p unity-app/Assets/Materials
mkdir -p unity-app/ProjectSettings

# Create basic Unity project files
if [ ! -f "unity-app/ProjectSettings/ProjectVersion.txt" ]; then
    cat > unity-app/ProjectSettings/ProjectVersion.txt << EOF
m_EditorVersion: 2022.3.15f1
m_EditorVersionWithRevision: 2022.3.15f1 (b58023a2b463)
EOF
fi

# Create development scripts
print_status "Creating development scripts..."

# Backend start script
cat > scripts/start_backend.sh << 'EOF'
#!/bin/bash
cd backend
source venv/bin/activate
uvicorn src.main:app --host 0.0.0.0 --port 8080 --reload
EOF

# Frontend start script
cat > scripts/start_frontend.sh << 'EOF'
#!/bin/bash
cd admin-frontend
npm run dev
EOF

# Make scripts executable
chmod +x scripts/start_backend.sh
chmod +x scripts/start_frontend.sh

# Create development documentation
print_status "Creating development documentation..."
cat > DEVELOPMENT.md << 'EOF'
# Development Guide

## Quick Start

1. **Start Backend**:
   ```bash
   ./scripts/start_backend.sh
   ```

2. **Start Admin Frontend**:
   ```bash
   ./scripts/start_frontend.sh
   ```

3. **Open Unity**:
   - Open Unity Hub
   - Add project from `unity-app/` directory
   - Open the project

## Development Workflow

1. Backend runs on http://localhost:8080
2. Admin frontend runs on http://localhost:3000
3. Unity connects to backend via WebSocket

## Testing PM5 Connection

Without physical PM5 devices, use the simulator:
```bash
cd backend
source venv/bin/activate
python src/pm5_simulator.py
```

## API Documentation

- Backend API: http://localhost:8080/docs
- WebSocket endpoints: ws://localhost:8080/ws/unity and ws://localhost:8080/ws/admin
EOF

print_success "Development environment setup complete!"
echo ""
print_status "Next steps:"
echo "1. Edit backend/.env with your configuration"
echo "2. Start the backend: ./scripts/start_backend.sh"
echo "3. Start the frontend: ./scripts/start_frontend.sh"
echo "4. Open Unity Hub and add the unity-app project"
echo "5. Read DEVELOPMENT.md for detailed instructions"
echo ""
print_warning "Note: You'll need to install Unity 2022.3 LTS manually from Unity Hub"
