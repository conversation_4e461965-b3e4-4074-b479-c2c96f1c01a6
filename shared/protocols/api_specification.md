# API Specification for Competition Management System

## Overview

This document defines the communication protocols between the three components:
- **Python Backend** ↔ **Unity Application** (WebSocket + REST)
- **Python Backend** ↔ **Admin Frontend** (REST + WebSocket)

## WebSocket Protocol (Real-time Data)

### Connection Endpoints
- Unity: `ws://localhost:8080/ws/unity`
- Admin Frontend: `ws://localhost:8080/ws/admin`

### Message Format
```json
{
  "type": "string",
  "timestamp": "ISO8601",
  "data": "object"
}
```

### Message Types

#### 1. Device Data (Backend → Unity/Admin)
```json
{
  "type": "device_data",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "device_id": "PM5_001",
    "user_id": "user_123",
    "metrics": {
      "time": 120.5,
      "distance": 500.0,
      "stroke_rate": 28,
      "power": 250,
      "pace": 120.0,
      "calories_per_hour": 800,
      "total_calories": 50,
      "heart_rate": 150,
      "force_plot": [10, 15, 20, 25, 20, 15, 10],
      "stroke_state": 2
    }
  }
}
```

#### 2. Device Status (Backend → Unity/Admin)
```json
{
  "type": "device_status",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "device_id": "PM5_001",
    "status": "connected",
    "last_seen": "2024-01-01T12:00:00Z",
    "device_info": {
      "model": "PM5",
      "serial_number": "12345678",
      "firmware_version": "1.2.3"
    }
  }
}
```

#### 3. Competition Update (Backend → Unity)
```json
{
  "type": "competition_update",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "competition_id": "comp_001",
    "status": "active",
    "participants": [
      {
        "user_id": "user_123",
        "device_id": "PM5_001",
        "position": {"x": 100.5, "y": 50.0},
        "rank": 1
      }
    ],
    "leaderboard": [
      {
        "user_id": "user_123",
        "name": "John Doe",
        "distance": 500.0,
        "time": 120.5,
        "rank": 1
      }
    ]
  }
}
```

#### 4. System Commands (Unity/Admin → Backend)
```json
{
  "type": "command",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "action": "start_competition",
    "parameters": {
      "competition_id": "comp_001",
      "duration": 300,
      "participants": ["user_123", "user_456"]
    }
  }
}
```

## REST API Endpoints

### Base URL: `http://localhost:8080/api/v1`

### Device Management

#### GET /devices
Get all connected devices
```json
{
  "devices": [
    {
      "device_id": "PM5_001",
      "status": "connected",
      "last_seen": "2024-01-01T12:00:00Z",
      "device_info": {
        "model": "PM5",
        "serial_number": "12345678",
        "firmware_version": "1.2.3"
      }
    }
  ]
}
```

#### POST /devices/{device_id}/register
Register a device with user
```json
{
  "user_id": "user_123",
  "user_name": "John Doe"
}
```

### User Management

#### GET /users
Get all users
```json
{
  "users": [
    {
      "user_id": "user_123",
      "name": "John Doe",
      "email": "<EMAIL>",
      "created_at": "2024-01-01T12:00:00Z"
    }
  ]
}
```

#### POST /users
Create new user
```json
{
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

### Competition Management

#### GET /competitions
Get all competitions
```json
{
  "competitions": [
    {
      "competition_id": "comp_001",
      "name": "Morning Sprint",
      "status": "active",
      "created_at": "2024-01-01T12:00:00Z",
      "participants": ["user_123", "user_456"]
    }
  ]
}
```

#### POST /competitions
Create new competition
```json
{
  "name": "Morning Sprint",
  "type": "distance",
  "target": 2000,
  "duration": 600,
  "participants": ["user_123", "user_456"]
}
```

#### POST /competitions/{competition_id}/start
Start competition
```json
{
  "countdown": 10
}
```

#### POST /competitions/{competition_id}/stop
Stop competition

### Data Export

#### GET /competitions/{competition_id}/export
Export competition data as CSV/JSON

#### GET /devices/{device_id}/history
Get historical data for device
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-01-02",
  "limit": 1000
}
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "DEVICE_NOT_FOUND",
    "message": "Device PM5_001 not found",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

### Error Codes
- `DEVICE_NOT_FOUND`: Device not connected
- `USER_NOT_FOUND`: User does not exist
- `COMPETITION_NOT_FOUND`: Competition does not exist
- `INVALID_REQUEST`: Malformed request
- `INTERNAL_ERROR`: Server error

## Authentication

### Admin Frontend
- JWT token-based authentication
- Role-based access control (admin, operator, viewer)

### Unity Application
- API key authentication for local communication
- No user authentication required
