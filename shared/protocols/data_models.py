"""
Data models for the Competition Management System
Shared between Python backend, Unity (via JSON), and Admin frontend
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass, asdict
import json


class DeviceStatus(Enum):
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    ERROR = "error"
    PAIRING = "pairing"


class CompetitionStatus(Enum):
    CREATED = "created"
    READY = "ready"
    COUNTDOWN = "countdown"
    ACTIVE = "active"
    PAUSED = "paused"
    FINISHED = "finished"
    CANCELLED = "cancelled"


class CompetitionType(Enum):
    DISTANCE = "distance"  # Fixed distance race
    TIME = "time"         # Fixed time race
    INTERVAL = "interval"  # Interval training
    FREE = "free"         # Free rowing


class StrokeState(Enum):
    WAITING_FOR_WHEEL = 0
    WHEEL_HAS_REACHED_MIN_SPEED = 1
    WHEEL_ACCELERATING = 2
    WHEEL_DECELERATING = 3
    WHEEL_STOPPED = 4


@dataclass
class DeviceInfo:
    model: str
    serial_number: str
    firmware_version: str
    hardware_version: Optional[str] = None


@dataclass
class PM5Metrics:
    time: float  # seconds
    distance: float  # meters
    stroke_rate: int  # strokes per minute
    power: int  # watts
    pace: float  # seconds per 500m
    calories_per_hour: int
    total_calories: int
    heart_rate: Optional[int] = None
    force_plot: Optional[List[int]] = None
    stroke_state: StrokeState = StrokeState.WAITING_FOR_WHEEL


@dataclass
class Device:
    device_id: str
    status: DeviceStatus
    last_seen: datetime
    device_info: DeviceInfo
    current_user_id: Optional[str] = None
    connection_type: str = "usb"  # "usb" or "bluetooth"


@dataclass
class User:
    user_id: str
    name: str
    email: Optional[str] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class Participant:
    user_id: str
    device_id: str
    position: Dict[str, float]  # {"x": 0.0, "y": 0.0, "z": 0.0}
    rank: int
    current_metrics: Optional[PM5Metrics] = None


@dataclass
class LeaderboardEntry:
    user_id: str
    name: str
    distance: float
    time: float
    rank: int
    power: int
    pace: float


@dataclass
class Competition:
    competition_id: str
    name: str
    type: CompetitionType
    status: CompetitionStatus
    created_at: datetime
    target: Optional[float] = None  # distance in meters or time in seconds
    duration: Optional[int] = None  # max duration in seconds
    participants: List[str] = None  # user_ids
    started_at: Optional[datetime] = None
    finished_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.participants is None:
            self.participants = []
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class DeviceDataMessage:
    device_id: str
    user_id: Optional[str]
    metrics: PM5Metrics
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class CompetitionUpdate:
    competition_id: str
    status: CompetitionStatus
    participants: List[Participant]
    leaderboard: List[LeaderboardEntry]
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class WebSocketMessage:
    type: str
    timestamp: datetime
    data: Dict[str, Any]
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class DataModelEncoder(json.JSONEncoder):
    """Custom JSON encoder for data models"""
    
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Enum):
            return obj.value
        elif hasattr(obj, '__dict__'):
            return asdict(obj)
        return super().default(obj)


def to_json(obj) -> str:
    """Convert data model to JSON string"""
    return json.dumps(obj, cls=DataModelEncoder, indent=2)


def create_websocket_message(msg_type: str, data: Any) -> WebSocketMessage:
    """Create a WebSocket message with proper formatting"""
    return WebSocketMessage(
        type=msg_type,
        timestamp=datetime.now(),
        data=data if isinstance(data, dict) else asdict(data)
    )


# Unity-specific position calculations
def calculate_race_position(distance: float, total_distance: float, track_length: float = 1000.0) -> Dict[str, float]:
    """
    Calculate 2D position for Unity visualization
    
    Args:
        distance: Current distance rowed (meters)
        total_distance: Total race distance (meters)
        track_length: Length of the visual track in Unity units
    
    Returns:
        Dictionary with x, y, z coordinates
    """
    progress = min(distance / total_distance, 1.0) if total_distance > 0 else 0.0
    x_position = progress * track_length
    
    return {
        "x": x_position,
        "y": 0.0,
        "z": 0.0
    }


# Database schema helpers
def get_database_schema() -> Dict[str, str]:
    """Return SQLite database schema"""
    return {
        "devices": """
            CREATE TABLE IF NOT EXISTS devices (
                device_id TEXT PRIMARY KEY,
                status TEXT NOT NULL,
                last_seen TIMESTAMP NOT NULL,
                model TEXT NOT NULL,
                serial_number TEXT NOT NULL,
                firmware_version TEXT NOT NULL,
                hardware_version TEXT,
                connection_type TEXT DEFAULT 'usb',
                current_user_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """,
        "users": """
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """,
        "competitions": """
            CREATE TABLE IF NOT EXISTS competitions (
                competition_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                status TEXT NOT NULL,
                target REAL,
                duration INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                started_at TIMESTAMP,
                finished_at TIMESTAMP
            )
        """,
        "competition_participants": """
            CREATE TABLE IF NOT EXISTS competition_participants (
                competition_id TEXT,
                user_id TEXT,
                device_id TEXT,
                joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (competition_id, user_id),
                FOREIGN KEY (competition_id) REFERENCES competitions(competition_id),
                FOREIGN KEY (user_id) REFERENCES users(user_id),
                FOREIGN KEY (device_id) REFERENCES devices(device_id)
            )
        """,
        "device_data": """
            CREATE TABLE IF NOT EXISTS device_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id TEXT NOT NULL,
                user_id TEXT,
                competition_id TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                time REAL,
                distance REAL,
                stroke_rate INTEGER,
                power INTEGER,
                pace REAL,
                calories_per_hour INTEGER,
                total_calories INTEGER,
                heart_rate INTEGER,
                stroke_state INTEGER,
                FOREIGN KEY (device_id) REFERENCES devices(device_id),
                FOREIGN KEY (user_id) REFERENCES users(user_id),
                FOREIGN KEY (competition_id) REFERENCES competitions(competition_id)
            )
        """
    }
