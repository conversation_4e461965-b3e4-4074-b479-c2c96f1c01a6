# PM5 Communication Protocol

## Overview

The Concept2 PM5 ergometer supports communication via USB using the CSAFE (Communications Specification for Fitness Equipment) protocol. Based on research, we'll use a combination of existing libraries and custom implementation.

## Communication Methods

### Primary: USB Serial Communication
- **Protocol**: CSAFE over USB serial
- **Library**: Modified PyRow library (supports PM3/PM4, needs PM5 adaptation)
- **Connection**: USB cable to Mac OS X M1
- **Advantages**: Reliable, low latency, well-documented

### Secondary: Bluetooth Low Energy (BLE)
- **Protocol**: Custom BLE GATT services
- **Library**: `bleak` Python library for cross-platform BLE
- **Connection**: Bluetooth pairing
- **Advantages**: Wireless, multiple device support

## Data Structure

### Real-time Data Points
```json
{
  "device_id": "string",
  "timestamp": "ISO8601",
  "data": {
    "time": "number (seconds)",
    "distance": "number (meters)",
    "stroke_rate": "number (strokes per minute)",
    "power": "number (watts)",
    "pace": "number (seconds per 500m)",
    "calories_per_hour": "number",
    "total_calories": "number",
    "heart_rate": "number (bpm)",
    "force_plot": "array of numbers",
    "stroke_state": "number (0-4)",
    "status": "number (0-9)"
  }
}
```

### Device Status
```json
{
  "device_id": "string",
  "status": "string (connected|disconnected|error)",
  "last_seen": "ISO8601",
  "device_info": {
    "model": "string",
    "serial_number": "string",
    "firmware_version": "string"
  }
}
```

## CSAFE Commands

### Essential Commands
- `CSAFE_GETMONITOR_CMD`: Get current workout data
- `CSAFE_GETFORCEPLOT_CMD`: Get force curve data
- `CSAFE_GETSTATUS_CMD`: Get machine status
- `CSAFE_GETWORKOUT_CMD`: Get workout information
- `CSAFE_SETTWORK_CMD`: Set workout parameters

### Status Codes
- 0: Error
- 1: Ready
- 2: Idle
- 3: Have ID
- 4: N/A
- 5: In Use
- 6: Pause
- 7: Finished
- 8: Manual
- 9: Offline

## Implementation Strategy

1. **USB Connection**: Primary method using modified PyRow
2. **Device Discovery**: Scan for connected PM5 devices
3. **Data Polling**: Continuous polling at 10Hz for real-time data
4. **Error Handling**: Robust reconnection and error recovery
5. **Multi-device Support**: Handle multiple ergometers simultaneously

## Python Libraries Required

```python
# USB Serial Communication
pyserial>=3.5
pyusb>=1.2.1

# Bluetooth Low Energy (backup)
bleak>=0.20.0

# Data processing
numpy>=1.21.0
pandas>=1.3.0
```

## Connection Flow

1. **Discovery**: Scan for PM5 devices via USB/Bluetooth
2. **Registration**: Register device with unique identifier
3. **Handshake**: Establish CSAFE communication
4. **Polling**: Start continuous data collection
5. **Monitoring**: Track connection status and handle errors
